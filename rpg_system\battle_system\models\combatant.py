"""
戰鬥單位（Combatant）領域模型
"""

from dataclasses import dataclass, field
from typing import List, Dict, Any, Optional, Tuple
import uuid

from .skill_instance import SkillInstance
from .status_effect_instance import StatusEffectInstance


@dataclass
class Combatant:
    """戰鬥單位領域模型"""
    
    # 靜態/初始化時設定的屬性
    instance_id: str = field(default_factory=lambda: str(uuid.uuid4()))
    definition_id: str = ""  # 卡牌ID或怪物ID
    name: str = ""
    is_player_side: bool = True
    rpg_level: int = 1
    star_level: int = 0  # 0-35，對於玩家卡牌
    position: int = 0  # 站位索引
    
    # 技能相關
    skill_order_preference: List[str] = field(default_factory=list)  # 技能ID優先順序
    primary_attack_skill: Optional[SkillInstance] = None
    active_skills: List[SkillInstance] = field(default_factory=list)
    innate_passive: Optional[SkillInstance] = None
    common_passives: List[SkillInstance] = field(default_factory=list)
    
    # 動態戰鬥屬性
    max_hp: int = 100
    current_hp: int = 100
    max_mp: int = 50
    current_mp: int = 50
    current_stats: Dict[str, float] = field(default_factory=dict)  # 當前戰鬥屬性
    status_effects: List[StatusEffectInstance] = field(default_factory=list)
    
    def __post_init__(self):
        """初始化後處理"""
        if not self.current_stats:
            self.current_stats = {
                'patk': 10.0,
                'pdef': 10.0,
                'matk': 10.0,
                'mdef': 10.0,
                'spd': 10.0,
                'crit_rate': 0.05,
                'crit_dmg_multiplier': 1.5,
                'accuracy': 0.95,
                'evasion': 0.05
            }
    
    async def calculate_final_stats(self, config_loader, attribute_calculator=None) -> None:
        """
        根據戰鬥單位自身的屬性和裝備的技能等級，計算最終戰鬥屬性

        Args:
            config_loader: 配置加載器實例
            attribute_calculator: 屬性計算器實例，如果為None則創建新實例
        """
        from ..services.attribute_calculator import AttributeCalculator

        if attribute_calculator is None:
            attribute_calculator = AttributeCalculator()

        # 確定戰鬥單位類型
        combatant_type = "CARD" if self.is_player_side else "MONSTER"

        # 計算屬性
        calculated_attributes = await attribute_calculator.calculate_attributes(
            combatant_definition_id=self.definition_id,
            combatant_type=combatant_type,
            rpg_level=self.rpg_level,
            star_level=self.star_level,
            config_loader=config_loader
        )

        if calculated_attributes:
            # 更新最大生命值和法力值
            self.max_hp = int(calculated_attributes.get('max_hp', 100))
            self.max_mp = int(calculated_attributes.get('max_mp', 50))

            # 初始化當前生命值和法力值
            self.current_hp = self.max_hp
            self.current_mp = self.max_mp

            # 更新戰鬥屬性
            self.current_stats.update({
                'patk': calculated_attributes.get('patk', 10.0),
                'pdef': calculated_attributes.get('pdef', 10.0),
                'matk': calculated_attributes.get('matk', 10.0),
                'mdef': calculated_attributes.get('mdef', 10.0),
                'spd': calculated_attributes.get('spd', 10.0),
                'crit_rate': calculated_attributes.get('crit_rate', 0.05),
                'crit_dmg_multiplier': calculated_attributes.get('crit_dmg_multiplier', 1.5),
                'accuracy': calculated_attributes.get('accuracy', 0.95),
                'evasion': calculated_attributes.get('evasion', 0.05),
                'mp_regen_per_turn': calculated_attributes.get('mp_regen_per_turn', 0.0)
            })
    
    async def take_damage(self, amount: float, damage_type: str, is_crit: bool, battle_context: Any) -> float:
        """
        處理傷害
        
        Args:
            amount: 傷害數值
            damage_type: 傷害類型
            is_crit: 是否暴擊
            battle_context: 戰鬥上下文
            
        Returns:
            實際造成的傷害
        """
        # 確保傷害不為負數
        actual_damage = max(0, amount)

        # 記錄傷害前的生命值
        old_hp = self.current_hp

        # 扣除生命值
        self.current_hp = max(0, self.current_hp - int(actual_damage))

        # 檢查是否死亡
        if old_hp > 0 and self.current_hp <= 0:
            # 觸發死亡事件
            if battle_context and hasattr(battle_context, 'trigger_death_event'):
                await battle_context.trigger_death_event(self)

        # 可能觸發被動技能
        # 這裡會調用 PassiveTriggerHandler

        return actual_damage
    
    async def heal(self, amount: float, is_crit_heal: bool, battle_context: Any) -> float:
        """
        處理治療
        
        Args:
            amount: 治療數值
            is_crit_heal: 是否暴擊治療
            battle_context: 戰鬥上下文
            
        Returns:
            實際治療量
        """
        # 確保治療量不為負數
        actual_heal = max(0, amount)
        
        # 恢復生命值，不超過最大值
        old_hp = self.current_hp
        self.current_hp = min(self.max_hp, self.current_hp + int(actual_heal))
        
        actual_heal = self.current_hp - old_hp
        
        # 可能觸發被動技能
        
        return actual_heal
    
    def consume_mp(self, amount: int) -> bool:
        """
        消耗法力值
        
        Args:
            amount: 消耗的MP數量
            
        Returns:
            是否成功消耗
        """
        if self.current_mp >= amount:
            self.current_mp -= amount
            return True
        return False
    
    async def add_status_effect(self, status_effect_instance: StatusEffectInstance, battle_context: Any) -> None:
        """
        添加狀態效果
        
        Args:
            status_effect_instance: 狀態效果實例
            battle_context: 戰鬥上下文
        """
        # 檢查是否已存在相同ID的狀態效果
        existing_effect = None
        for effect in self.status_effects:
            if effect.status_effect_id == status_effect_instance.status_effect_id:
                existing_effect = effect
                break
        
        if existing_effect:
            # 處理疊加邏輯
            await self._handle_status_effect_stacking(existing_effect, status_effect_instance)
        else:
            # 添加新的狀態效果
            self.status_effects.append(status_effect_instance)
    
    async def remove_status_effect(self, status_effect_id: str, battle_context: Any) -> bool:
        """
        移除狀態效果
        
        Args:
            status_effect_id: 狀態效果ID
            battle_context: 戰鬥上下文
            
        Returns:
            是否成功移除
        """
        for i, effect in enumerate(self.status_effects):
            if effect.status_effect_id == status_effect_id:
                self.status_effects.pop(i)
                return True
        return False
    
    async def get_available_action(self, battle_context: Any) -> Tuple[str, List[str]]:
        """
        核心決策邏輯，獲取可用的行動
        
        Args:
            battle_context: 戰鬥上下文
            
        Returns:
            (技能ID, 建議目標ID列表)
        """
        # 按照 skill_order_preference 順序檢查技能
        for skill_id in self.skill_order_preference:
            skill_instance = self.get_skill_instance(skill_id)
            if skill_instance and skill_instance.is_usable(self.current_mp):
                return skill_id, []  # 目標選擇由 TargetSelector 處理
        
        # 如果沒有可用的主動技能，使用普攻
        if self.primary_attack_skill:
            return self.primary_attack_skill.skill_id, []
        
        # 如果連普攻都沒有，返回空行動
        return "", []
    
    async def apply_turn_start_effects(self, battle_context: Any) -> None:
        """應用回合開始時觸發的被動和狀態效果"""
        # 處理狀態效果
        await self.tick_status_effects(battle_context)
        
        # 減少技能冷卻
        self.tick_cooldowns()
        
        # 觸發被動技能
        # 這裡會調用 PassiveTriggerHandler
    
    async def apply_turn_end_effects(self, battle_context: Any) -> None:
        """應用回合結束時觸發的被動和狀態效果"""
        # 觸發被動技能
        # 這裡會調用 PassiveTriggerHandler
        pass
    
    def tick_cooldowns(self) -> None:
        """更新所有主動技能的冷卻時間"""
        for skill in self.active_skills:
            if skill.current_cooldown > 0:
                skill.current_cooldown -= 1
        
        if self.primary_attack_skill and self.primary_attack_skill.current_cooldown > 0:
            self.primary_attack_skill.current_cooldown -= 1
    
    async def tick_status_effects(self, battle_context: Any) -> None:
        """更新狀態效果的持續時間並觸發其周期性效果"""
        effects_to_remove = []
        
        for effect in self.status_effects:
            # 觸發狀態效果的周期性效果
            # 這裡會調用 EffectApplier
            
            # 減少持續時間
            if effect.duration_turns > 0:
                effect.duration_turns -= 1
                if effect.duration_turns <= 0:
                    effects_to_remove.append(effect)
        
        # 移除過期的狀態效果
        for effect in effects_to_remove:
            self.status_effects.remove(effect)
    
    def is_alive(self) -> bool:
        """判斷是否存活"""
        return self.current_hp > 0
    
    async def can_act(self, battle_context: Any) -> bool:
        """判斷是否能夠行動"""
        # 檢查是否有 CANNOT_ACT 狀態
        for effect in self.status_effects:
            # 這裡需要檢查狀態效果的定義
            # 暫時簡化實現
            pass
        
        return self.is_alive()
    
    def get_skill_instance(self, skill_id: str) -> Optional[SkillInstance]:
        """根據技能ID獲取技能實例"""
        # 檢查主動技能
        for skill in self.active_skills:
            if skill.skill_id == skill_id:
                return skill
        
        # 檢查普攻
        if self.primary_attack_skill and self.primary_attack_skill.skill_id == skill_id:
            return self.primary_attack_skill
        
        return None
    
    async def _handle_status_effect_stacking(self, existing: StatusEffectInstance, new: StatusEffectInstance) -> None:
        """處理狀態效果疊加邏輯"""
        # 這裡需要根據狀態效果的配置來處理疊加
        # 暫時簡化為刷新持續時間
        existing.duration_turns = new.duration_turns
