"""
效果應用器 (EffectApplier)
RPG系統的效果引擎核心，負責處理所有類型的效果應用
"""

from typing import List, Dict, Any, Optional, TYPE_CHECKING
from utils.logger import logger
from ..models.skill_instance import SkillType

if TYPE_CHECKING:
    from ..models.combatant import Combatant
    from ..models.battle import Battle
    from ..models.skill_instance import SkillInstance
    from ..models.status_effect_instance import StatusEffectInstance
    from ...formula_engine.evaluator import FormulaEvaluator
    from ...config.loader import ConfigLoader
    from .target_selector import TargetSelector
    from .damage_handler import DamageHandler


class EffectApplier:
    """效果應用器服務 - 效果系統的核心協調者"""
    
    def __init__(
        self,
        formula_evaluator: 'FormulaEvaluator',
        target_selector: 'TargetSelector',
        config_loader: 'ConfigLoader',
        damage_handler: Optional['DamageHandler'] = None
    ):
        """
        初始化效果應用器
        
        Args:
            formula_evaluator: 公式求值器
            target_selector: 目標選擇器
            config_loader: 配置加載器
            damage_handler: 傷害處理器（可選）
        """
        self.formula_evaluator = formula_evaluator
        self.target_selector = target_selector
        self.config_loader = config_loader
        self.damage_handler = damage_handler
    
    async def apply_skill_effects(
        self,
        caster: 'Combatant',
        targets: List['Combatant'],
        skill_instance: 'SkillInstance',
        battle_context: 'Battle'
    ) -> List[Dict[str, Any]]:
        """
        應用技能效果
        
        Args:
            caster: 施法者
            targets: 預選目標列表
            skill_instance: 技能實例
            battle_context: 戰鬥上下文
            
        Returns:
            效果應用結果列表
        """
        try:
            # 獲取技能定義
            all_configs = await self.config_loader.get_all_configs()
            skill_definition = await skill_instance.get_definition(all_configs)
            
            if not skill_definition:
                logger.warning(f"無法找到技能定義: {skill_instance.skill_id}")
                return []
            
            # 根據技能類型獲取效果定義
            effect_definitions = []
            if skill_instance.skill_type == SkillType.INNATE_PASSIVE:
                # 天賦技能根據星級獲取效果
                star_level = caster.star_level
                effects_by_star = skill_definition.get('effects_by_star_level', {})
                effect_definitions = effects_by_star.get(str(star_level), {}).get('effect_definitions', [])
            else:
                # 普通技能使用新的增量式結構
                effect_definitions = skill_definition.get('effect_definitions', [])
            
            if not effect_definitions:
                logger.warning(f"技能 {skill_instance.skill_id} 沒有效果定義")
                return []
            
            # 獲取技能標籤
            skill_tags = skill_definition.get('tags', [])
            
            # 應用效果定義
            return await self.apply_effect_definitions(
                caster=caster,
                initial_targets=targets,
                effect_definitions=effect_definitions,
                battle_context=battle_context,
                source_skill_tags=skill_tags,
                source_skill_instance=skill_instance
            )
            
        except Exception as e:
            logger.error(f"應用技能效果錯誤: {e}")
            return []
    
    async def apply_effect_definitions(
        self,
        caster: 'Combatant',
        initial_targets: List['Combatant'],
        effect_definitions: List[Dict[str, Any]],
        battle_context: 'Battle',
        source_skill_tags: Optional[List[str]] = None,
        source_skill_instance: Optional['SkillInstance'] = None,
        custom_vars_from_source: Optional[Dict[str, Any]] = None
    ) -> List[Dict[str, Any]]:
        """
        應用效果定義列表
        
        Args:
            caster: 施法者
            initial_targets: 初始目標列表
            effect_definitions: 效果定義列表
            battle_context: 戰鬥上下文
            source_skill_tags: 來源技能標籤
            source_skill_instance: 來源技能實例
            custom_vars_from_source: 自定義變量
            
        Returns:
            效果應用結果列表
        """
        results = []
        source_skill_tags = source_skill_tags or []
        custom_vars_from_source = custom_vars_from_source or {}
        
        for effect_def in effect_definitions:
            try:
                # 處理效果模板
                processed_effect = await self._process_effect_template(effect_def)
                
                # 確定最終目標
                actual_targets = await self._determine_effect_targets(
                    caster, initial_targets, processed_effect, battle_context
                )
                
                # 對每個目標應用效果
                for target in actual_targets:
                    effect_result = await self._apply_single_effect(
                        caster=caster,
                        target=target,
                        effect_def=processed_effect,
                        battle_context=battle_context,
                        source_skill_tags=source_skill_tags,
                        source_skill_instance=source_skill_instance,
                        custom_vars=custom_vars_from_source
                    )
                    
                    if effect_result:
                        results.append(effect_result)
                        
            except Exception as e:
                logger.error(f"應用效果定義錯誤: {e}")
                continue
        
        return results
    
    async def _process_effect_template(self, effect_def: Dict[str, Any]) -> Dict[str, Any]:
        """
        處理效果模板
        
        Args:
            effect_def: 效果定義
            
        Returns:
            處理後的效果定義
        """
        # 如果使用了效果模板，則從配置中加載模板並合併
        effect_template = effect_def.get('effect_template')
        if effect_template:
            all_configs = await self.config_loader.get_all_configs()
            effect_templates = all_configs.get('effect_templates', {})
            template_def = effect_templates.get(effect_template, {})
            
            # 合併模板和當前定義（當前定義優先）
            processed_effect = template_def.copy()
            processed_effect.update({k: v for k, v in effect_def.items() if k != 'effect_template'})
            return processed_effect
        
        return effect_def.copy()
    
    async def _determine_effect_targets(
        self,
        caster: 'Combatant',
        initial_targets: List['Combatant'],
        effect_def: Dict[str, Any],
        battle_context: 'Battle'
    ) -> List['Combatant']:
        """
        確定效果的最終目標
        
        Args:
            caster: 施法者
            initial_targets: 初始目標列表
            effect_def: 效果定義
            battle_context: 戰鬥上下文
            
        Returns:
            最終目標列表
        """
        # 檢查是否有目標覆蓋邏輯
        target_override = effect_def.get('target_override')
        if target_override:
            return await self.target_selector.select_targets(
                caster, target_override, battle_context, 
                self.formula_evaluator, self.config_loader
            )
        
        return initial_targets
    
    async def _apply_single_effect(
        self,
        caster: 'Combatant',
        target: 'Combatant',
        effect_def: Dict[str, Any],
        battle_context: 'Battle',
        source_skill_tags: List[str],
        source_skill_instance: Optional['SkillInstance'] = None,
        custom_vars: Optional[Dict[str, Any]] = None
    ) -> Optional[Dict[str, Any]]:
        """
        對單個目標應用單個效果
        
        Args:
            caster: 施法者
            target: 目標
            effect_def: 效果定義
            battle_context: 戰鬥上下文
            source_skill_tags: 來源技能標籤
            source_skill_instance: 來源技能實例
            custom_vars: 自定義變量
            
        Returns:
            效果應用結果
        """
        try:
            # 準備效果公式上下文
            context_vars = self._prepare_effect_context(
                caster, target, battle_context, source_skill_instance, custom_vars
            )
            
            # 檢查應用條件
            if not self._check_effect_conditions(effect_def, context_vars):
                return None
            
            # 根據效果類型分發處理
            effect_type = effect_def.get('effect_type')
            
            if effect_type == 'DAMAGE':
                return await self._apply_damage_effect(
                    caster, target, effect_def, context_vars, 
                    battle_context, source_skill_tags
                )
            elif effect_type == 'HEAL':
                return await self._apply_heal_effect(
                    caster, target, effect_def, context_vars, battle_context
                )
            elif effect_type == 'APPLY_STATUS_EFFECT':
                return await self._apply_status_effect_application(
                    caster, target, effect_def, context_vars, battle_context
                )
            elif effect_type == 'STAT_MODIFICATION':
                return await self._apply_stat_modification_effect(
                    caster, target, effect_def, context_vars, battle_context
                )
            elif effect_type == 'GAIN_MP':
                return await self._apply_mp_gain_effect(
                    target, effect_def, context_vars, battle_context
                )
            elif effect_type == 'LOSE_MP':
                return await self._apply_mp_loss_effect(
                    target, effect_def, context_vars, battle_context
                )
            else:
                logger.warning(f"未知的效果類型: {effect_type}")
                return None
                
        except Exception as e:
            logger.error(f"應用單個效果錯誤: {e}")
            return None
    
    def _prepare_effect_context(
        self,
        caster: 'Combatant',
        target: 'Combatant',
        battle_context: 'Battle',
        source_skill_instance: Optional['SkillInstance'] = None,
        custom_vars: Optional[Dict[str, Any]] = None
    ) -> Dict[str, Any]:
        """
        準備效果公式上下文
        
        Args:
            caster: 施法者
            target: 目標
            battle_context: 戰鬥上下文
            source_skill_instance: 來源技能實例
            custom_vars: 自定義變量
            
        Returns:
            上下文變量字典
        """
        context = {
            # 施法者相關
            'caster_star_level': caster.star_level,
            'caster_rpg_level': caster.rpg_level,
            'caster_stat_hp': caster.current_hp,
            'caster_stat_max_hp': caster.max_hp,
            'caster_stat_mp': caster.current_mp,
            'caster_stat_max_mp': caster.max_mp,
            'caster_stat_patk': caster.current_stats.get('patk', 0),
            'caster_stat_pdef': caster.current_stats.get('pdef', 0),
            'caster_stat_matk': caster.current_stats.get('matk', 0),
            'caster_stat_mdef': caster.current_stats.get('mdef', 0),
            'caster_stat_spd': caster.current_stats.get('spd', 0),
            'caster_stat_crit_rate': caster.current_stats.get('crit_rate', 0),
            'caster_stat_crit_dmg_multiplier': caster.current_stats.get('crit_dmg_multiplier', 1.5),
            'caster_current_hp_percent': caster.current_hp / max(caster.max_hp, 1),
            'caster_missing_hp_percent': 1 - (caster.current_hp / max(caster.max_hp, 1)),
            
            # 目標相關
            'target_stat_hp': target.current_hp,
            'target_stat_max_hp': target.max_hp,
            'target_stat_mp': target.current_mp,
            'target_stat_max_mp': target.max_mp,
            'target_stat_patk': target.current_stats.get('patk', 0),
            'target_stat_pdef': target.current_stats.get('pdef', 0),
            'target_stat_matk': target.current_stats.get('matk', 0),
            'target_stat_mdef': target.current_stats.get('mdef', 0),
            'target_stat_spd': target.current_stats.get('spd', 0),
            'target_current_hp_percent': target.current_hp / max(target.max_hp, 1),
            'target_missing_hp_percent': 1 - (target.current_hp / max(target.max_hp, 1)),
            'target_is_boss': 1 if getattr(target, 'is_boss', False) else 0,
            
            # 戰鬥相關
            'current_turn': battle_context.current_turn,
        }
        
        # 添加技能相關變量
        if source_skill_instance:
            context['skill_level'] = source_skill_instance.current_level
            # 對於天賦被動技能，star_level 就是 skill_level
            if source_skill_instance.skill_type.value == 'INNATE_PASSIVE':
                context['star_level'] = source_skill_instance.current_level

        # 添加自定義變量
        if custom_vars:
            context.update(custom_vars)

        return context
    
    def _check_effect_conditions(
        self, 
        effect_def: Dict[str, Any], 
        context_vars: Dict[str, Any]
    ) -> bool:
        """
        檢查效果應用條件
        
        Args:
            effect_def: 效果定義
            context_vars: 上下文變量
            
        Returns:
            是否滿足條件
        """
        conditions = effect_def.get('conditions_to_apply', [])
        if not conditions:
            return True
        
        for condition in conditions:
            condition_formula = condition.get('formula', '1')
            if not self.formula_evaluator.evaluate(condition_formula, context_vars):
                return False
        
        return True

    async def _apply_damage_effect(
        self,
        caster: 'Combatant',
        target: 'Combatant',
        effect_def: Dict[str, Any],
        context_vars: Dict[str, Any],
        battle_context: 'Battle',
        source_skill_tags: List[str]
    ) -> Dict[str, Any]:
        """
        應用傷害效果

        Args:
            caster: 施法者
            target: 目標
            effect_def: 效果定義
            context_vars: 上下文變量
            battle_context: 戰鬥上下文
            source_skill_tags: 來源技能標籤

        Returns:
            傷害效果結果
        """
        try:
            # 計算基礎傷害值
            base_damage = self._calculate_base_damage(effect_def, context_vars)

            # 如果有傷害處理器，使用它進行複雜計算
            if self.damage_handler:
                damage_result = await self.damage_handler.calculate_and_apply_damage(
                    caster=caster,
                    target=target,
                    damage_effect=effect_def,
                    base_damage_value=base_damage,
                    battle_context=battle_context,
                    config_loader=self.config_loader,
                    skill_tags=source_skill_tags
                )
            else:
                # 簡化的傷害處理
                actual_damage = target.take_damage(
                    base_damage,
                    effect_def.get('damage_type', 'PHYSICAL'),
                    False,
                    battle_context
                )
                damage_result = {
                    'final_damage': base_damage,
                    'actual_damage_dealt': actual_damage,
                    'was_crit': False,
                    'was_miss': False
                }

            # 觸發傷害相關事件
            await self._trigger_damage_events(caster, target, damage_result, effect_def, battle_context)

            return {
                'effect_type': 'DAMAGE',
                'caster_id': caster.instance_id,
                'target_id': target.instance_id,
                'success': True,
                **damage_result
            }

        except Exception as e:
            logger.error(f"應用傷害效果錯誤: {e}")
            return {
                'effect_type': 'DAMAGE',
                'caster_id': caster.instance_id,
                'target_id': target.instance_id,
                'success': False,
                'error': str(e)
            }

    def _calculate_base_damage(
        self,
        effect_def: Dict[str, Any],
        context_vars: Dict[str, Any]
    ) -> float:
        """
        計算基礎傷害值

        Args:
            effect_def: 效果定義
            context_vars: 上下文變量

        Returns:
            基礎傷害值
        """
        # 檢查是否有自定義公式（優先級最高）
        value_formula = effect_def.get('value_formula')
        if value_formula:
            return max(0, self.formula_evaluator.evaluate(value_formula, context_vars))

        # 獲取基礎威力倍率（支持公式）
        base_power_multiplier = effect_def.get('base_power_multiplier', 1.0)
        multiplier_formula = effect_def.get('multiplier_formula')
        if multiplier_formula:
            base_power_multiplier = self.formula_evaluator.evaluate(multiplier_formula, context_vars)
        elif effect_def.get('base_power_multiplier_formula'):
            base_power_multiplier = self.formula_evaluator.evaluate(
                effect_def.get('base_power_multiplier_formula'), context_vars
            )

        # 獲取固定傷害加值
        flat_damage_add = effect_def.get('flat_damage_add', 0.0)

        # 根據傷害類型選擇基礎攻擊力
        damage_type = effect_def.get('damage_type', 'PHYSICAL')
        if damage_type == 'PHYSICAL':
            base_attack = context_vars.get('caster_stat_patk', 0)
        elif damage_type == 'MAGICAL':
            base_attack = context_vars.get('caster_stat_matk', 0)
        else:
            base_attack = 0  # 真實傷害等特殊類型

        # 計算基礎傷害
        base_damage = (base_attack * base_power_multiplier) + flat_damage_add

        return max(0, base_damage)

    async def _apply_heal_effect(
        self,
        caster: 'Combatant',
        target: 'Combatant',
        effect_def: Dict[str, Any],
        context_vars: Dict[str, Any],
        battle_context: 'Battle'
    ) -> Dict[str, Any]:
        """
        應用治療效果

        Args:
            caster: 施法者
            target: 目標
            effect_def: 效果定義
            context_vars: 上下文變量
            battle_context: 戰鬥上下文

        Returns:
            治療效果結果
        """
        try:
            # 計算治療量
            heal_amount = self._calculate_heal_amount(effect_def, context_vars)

            # 應用治療
            actual_heal = target.heal(heal_amount, False, battle_context)

            # 觸發治療相關事件
            await self._trigger_heal_events(caster, target, heal_amount, actual_heal, battle_context)

            return {
                'effect_type': 'HEAL',
                'caster_id': caster.instance_id,
                'target_id': target.instance_id,
                'success': True,
                'heal_amount': heal_amount,
                'actual_heal': actual_heal
            }

        except Exception as e:
            logger.error(f"應用治療效果錯誤: {e}")
            return {
                'effect_type': 'HEAL',
                'caster_id': caster.instance_id,
                'target_id': target.instance_id,
                'success': False,
                'error': str(e)
            }

    def _calculate_heal_amount(
        self,
        effect_def: Dict[str, Any],
        context_vars: Dict[str, Any]
    ) -> float:
        """
        計算治療量

        Args:
            effect_def: 效果定義
            context_vars: 上下文變量

        Returns:
            治療量
        """
        heal_type = effect_def.get('heal_type', 'FLAT')
        value = effect_def.get('value', 0.0)

        if heal_type == 'FLAT':
            heal_amount = value
        elif heal_type == 'PERCENT_MAX_HP':
            max_hp = context_vars.get('target_stat_max_hp', 1)
            heal_amount = max_hp * value
        elif heal_type == 'PERCENT_CASTER_MATK':
            caster_matk = context_vars.get('caster_stat_matk', 0)
            heal_amount = caster_matk * value
        else:
            heal_amount = value

        # 如果有自定義公式，使用公式計算
        value_formula = effect_def.get('value_formula')
        if value_formula:
            heal_amount = self.formula_evaluator.evaluate(value_formula, context_vars)

        return max(0, heal_amount)

    async def _apply_status_effect_application(
        self,
        caster: 'Combatant',
        target: 'Combatant',
        effect_def: Dict[str, Any],
        context_vars: Dict[str, Any],
        battle_context: 'Battle'
    ) -> Dict[str, Any]:
        """
        應用狀態效果施加

        Args:
            caster: 施法者
            target: 目標
            effect_def: 效果定義
            context_vars: 上下文變量
            battle_context: 戰鬥上下文

        Returns:
            狀態效果應用結果
        """
        try:
            status_effect_id = effect_def.get('status_effect_id')
            if not status_effect_id:
                return {
                    'effect_type': 'APPLY_STATUS_EFFECT',
                    'success': False,
                    'error': 'Missing status_effect_id'
                }

            # 檢查施加機率
            chance = effect_def.get('chance', 1.0)
            if hasattr(battle_context, '_rng'):
                roll = battle_context._rng.random()
            else:
                import random
                roll = random.random()

            if roll > chance:
                return {
                    'effect_type': 'APPLY_STATUS_EFFECT',
                    'caster_id': caster.instance_id,
                    'target_id': target.instance_id,
                    'success': False,
                    'reason': 'Failed probability check'
                }

            # 創建狀態效果實例
            from ..models.status_effect_instance import StatusEffectInstance

            duration_turns = effect_def.get('duration_turns', 3)
            stack_count = effect_def.get('stack_count', 1)

            status_instance = StatusEffectInstance(
                status_effect_id=status_effect_id,
                caster_id=caster.instance_id,
                duration_turns=duration_turns,
                stack_count=stack_count
            )

            # 添加到目標
            target.add_status_effect(status_instance, battle_context)

            # 觸發狀態效果施加事件
            await self._trigger_status_effect_applied_event(
                caster, target, status_effect_id, duration_turns, stack_count, battle_context
            )

            return {
                'effect_type': 'APPLY_STATUS_EFFECT',
                'caster_id': caster.instance_id,
                'target_id': target.instance_id,
                'success': True,
                'status_effect_id': status_effect_id,
                'duration_turns': duration_turns,
                'stack_count': stack_count
            }

        except Exception as e:
            logger.error(f"應用狀態效果錯誤: {e}")
            return {
                'effect_type': 'APPLY_STATUS_EFFECT',
                'caster_id': caster.instance_id,
                'target_id': target.instance_id,
                'success': False,
                'error': str(e)
            }

    async def _apply_stat_modification_effect(
        self,
        caster: 'Combatant',
        target: 'Combatant',
        effect_def: Dict[str, Any],
        context_vars: Dict[str, Any],
        battle_context: 'Battle'
    ) -> Dict[str, Any]:
        """
        應用屬性修改效果

        Args:
            caster: 施法者
            target: 目標
            effect_def: 效果定義
            context_vars: 上下文變量
            battle_context: 戰鬥上下文

        Returns:
            屬性修改結果
        """
        try:
            modifications = effect_def.get('modifications', [])
            if not modifications:
                return {
                    'effect_type': 'STAT_MODIFICATION',
                    'success': False,
                    'error': 'No modifications specified'
                }

            applied_modifications = []

            for modification in modifications:
                stat_name = modification.get('stat_name')
                modification_type = modification.get('modification_type')
                value_formula = modification.get('value_formula', '0')

                if not stat_name or not modification_type:
                    continue

                # 計算修改值
                modification_value = self.formula_evaluator.evaluate(value_formula, context_vars)

                # 應用修改（這裡簡化實現，實際可能需要更複雜的屬性修改系統）
                current_value = target.current_stats.get(stat_name, 0)

                if modification_type == 'FLAT_ADD':
                    new_value = current_value + modification_value
                elif modification_type == 'PERCENTAGE_ADD':
                    new_value = current_value * (1 + modification_value)
                else:
                    new_value = current_value

                target.current_stats[stat_name] = max(0, new_value)

                applied_modifications.append({
                    'stat_name': stat_name,
                    'modification_type': modification_type,
                    'value': modification_value,
                    'old_value': current_value,
                    'new_value': target.current_stats[stat_name]
                })

            return {
                'effect_type': 'STAT_MODIFICATION',
                'caster_id': caster.instance_id,
                'target_id': target.instance_id,
                'success': True,
                'modifications': applied_modifications
            }

        except Exception as e:
            logger.error(f"應用屬性修改效果錯誤: {e}")
            return {
                'effect_type': 'STAT_MODIFICATION',
                'caster_id': caster.instance_id,
                'target_id': target.instance_id,
                'success': False,
                'error': str(e)
            }

    async def _apply_mp_gain_effect(
        self,
        target: 'Combatant',
        effect_def: Dict[str, Any],
        context_vars: Dict[str, Any],
        battle_context: 'Battle'
    ) -> Dict[str, Any]:
        """
        應用MP恢復效果

        Args:
            target: 目標
            effect_def: 效果定義
            context_vars: 上下文變量
            battle_context: 戰鬥上下文

        Returns:
            MP恢復結果
        """
        try:
            # 計算MP恢復量
            value_formula = effect_def.get('value_formula', '0')
            mp_gain = self.formula_evaluator.evaluate(value_formula, context_vars)

            # 應用MP恢復
            old_mp = target.current_mp
            target.current_mp = min(target.max_mp, target.current_mp + int(mp_gain))
            actual_gain = target.current_mp - old_mp

            return {
                'effect_type': 'GAIN_MP',
                'target_id': target.instance_id,
                'success': True,
                'mp_gain': mp_gain,
                'actual_gain': actual_gain,
                'old_mp': old_mp,
                'new_mp': target.current_mp
            }

        except Exception as e:
            logger.error(f"應用MP恢復效果錯誤: {e}")
            return {
                'effect_type': 'GAIN_MP',
                'target_id': target.instance_id,
                'success': False,
                'error': str(e)
            }

    async def _apply_mp_loss_effect(
        self,
        target: 'Combatant',
        effect_def: Dict[str, Any],
        context_vars: Dict[str, Any],
        battle_context: 'Battle'
    ) -> Dict[str, Any]:
        """
        應用MP消耗效果

        Args:
            target: 目標
            effect_def: 效果定義
            context_vars: 上下文變量
            battle_context: 戰鬥上下文

        Returns:
            MP消耗結果
        """
        try:
            # 計算MP消耗量
            value_formula = effect_def.get('value_formula', '0')
            mp_loss = self.formula_evaluator.evaluate(value_formula, context_vars)

            # 應用MP消耗
            old_mp = target.current_mp
            target.current_mp = max(0, target.current_mp - int(mp_loss))
            actual_loss = old_mp - target.current_mp

            return {
                'effect_type': 'LOSE_MP',
                'target_id': target.instance_id,
                'success': True,
                'mp_loss': mp_loss,
                'actual_loss': actual_loss,
                'old_mp': old_mp,
                'new_mp': target.current_mp
            }

        except Exception as e:
            logger.error(f"應用MP消耗效果錯誤: {e}")
            return {
                'effect_type': 'LOSE_MP',
                'target_id': target.instance_id,
                'success': False,
                'error': str(e)
            }

    async def _trigger_damage_events(
        self,
        caster: 'Combatant',
        target: 'Combatant',
        damage_result: Dict[str, Any],
        effect_def: Dict[str, Any],
        battle_context: 'Battle'
    ) -> None:
        """
        觸發傷害相關事件

        Args:
            caster: 施法者
            target: 目標
            damage_result: 傷害結果
            effect_def: 效果定義
            battle_context: 戰鬥上下文
        """
        try:
            # 如果沒有造成實際傷害，不觸發事件
            if damage_result.get('actual_damage_dealt', 0) <= 0:
                return

            # 準備事件數據
            damage_amount = damage_result.get('actual_damage_dealt', 0)
            damage_type = effect_def.get('damage_type', 'PHYSICAL')
            is_crit = damage_result.get('was_crit', False)
            is_fatal = target.current_hp <= 0

            # 觸發 ON_DAMAGE_DEALT 事件（施法者視角）
            damage_dealt_event_data = {
                'source_attacker_id': caster.instance_id,
                'target_id': target.instance_id,
                'damage_amount': damage_amount,
                'damage_type': damage_type,
                'is_crit': is_crit,
                'is_fatal_to_target': is_fatal,
                'is_primary_attack_damage': False,  # 這裡可以根據技能類型判斷
                'is_skill_damage': True
            }

            # 觸發 ON_DAMAGE_TAKEN 事件（目標視角）
            damage_taken_event_data = {
                'target_id': target.instance_id,
                'source_attacker_id': caster.instance_id,
                'damage_amount': damage_amount,
                'damage_type': damage_type,
                'is_crit': is_crit,
                'was_fatal': is_fatal
            }

            # 調用戰鬥上下文的事件觸發方法
            if hasattr(battle_context, 'trigger_event'):
                await battle_context.trigger_event('ON_DAMAGE_DEALT', damage_dealt_event_data)
                await battle_context.trigger_event('ON_DAMAGE_TAKEN', damage_taken_event_data)

        except Exception as e:
            logger.error(f"觸發傷害事件錯誤: {e}")

    async def _trigger_heal_events(
        self,
        caster: 'Combatant',
        target: 'Combatant',
        heal_amount: float,
        actual_heal: float,
        battle_context: 'Battle'
    ) -> None:
        """
        觸發治療相關事件

        Args:
            caster: 施法者
            target: 目標
            heal_amount: 計算的治療量
            actual_heal: 實際治療量
            battle_context: 戰鬥上下文
        """
        try:
            if actual_heal <= 0:
                return

            # 觸發 ON_HEAL_DEALT 事件（施法者視角）
            heal_dealt_event_data = {
                'source_healer_id': caster.instance_id,
                'target_id': target.instance_id,
                'heal_amount': actual_heal,
                'is_crit_heal': False  # 可以擴展支持暴擊治療
            }

            # 觸發 ON_HEAL_RECEIVED 事件（目標視角）
            heal_received_event_data = {
                'target_id': target.instance_id,
                'source_healer_id': caster.instance_id,
                'heal_amount': actual_heal,
                'is_crit_heal': False
            }

            # 調用戰鬥上下文的事件觸發方法
            if hasattr(battle_context, 'trigger_event'):
                await battle_context.trigger_event('ON_HEAL_DEALT', heal_dealt_event_data)
                await battle_context.trigger_event('ON_HEAL_RECEIVED', heal_received_event_data)

        except Exception as e:
            logger.error(f"觸發治療事件錯誤: {e}")

    async def _trigger_status_effect_applied_event(
        self,
        caster: 'Combatant',
        target: 'Combatant',
        status_effect_id: str,
        duration_turns: int,
        stack_count: int,
        battle_context: 'Battle'
    ) -> None:
        """
        觸發狀態效果施加事件

        Args:
            caster: 施法者
            target: 目標
            status_effect_id: 狀態效果ID
            duration_turns: 持續回合數
            stack_count: 層數
            battle_context: 戰鬥上下文
        """
        try:
            # 獲取狀態效果配置以判斷是否為Buff
            all_configs = self.config_loader.get_all_configs()
            status_effects = all_configs.get('status_effects', {})
            status_config = status_effects.get(status_effect_id, {})
            is_buff = status_config.get('is_buff', False)

            # 觸發 ON_STATUS_EFFECT_APPLIED 事件
            status_applied_event_data = {
                'target_id': target.instance_id,
                'source_caster_id': caster.instance_id,
                'status_effect_id': status_effect_id,
                'is_buff': is_buff,
                'duration_turns': duration_turns,
                'stack_count': stack_count
            }

            # 調用戰鬥上下文的事件觸發方法
            if hasattr(battle_context, 'trigger_event'):
                await battle_context.trigger_event('ON_STATUS_EFFECT_APPLIED', status_applied_event_data)

        except Exception as e:
            logger.error(f"觸發狀態效果施加事件錯誤: {e}")
