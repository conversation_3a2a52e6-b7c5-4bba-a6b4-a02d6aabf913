"""
戰鬥服務
整合所有戰鬥系統組件，提供完整的戰鬥功能
"""

from typing import Dict, Any, List, Optional, TYPE_CHECKING
from utils.logger import logger

if TYPE_CHECKING:
    from rpg_system.config.loader import ConfigLoader
    from rpg_system.formula_engine.evaluator import FormulaEvaluator
    from rpg_system.battle_system.models.battle import Battle
    from rpg_system.battle_system.models.combatant import Combatant
    from rpg_system.battle_system.handlers.effect_applier import EffectApplier
    from rpg_system.battle_system.handlers.target_selector import TargetSelector
    from rpg_system.battle_system.handlers.damage_handler import DamageHandler
    from rpg_system.battle_system.handlers.passive_trigger_handler import PassiveTriggerHandler
    from rpg_system.battle_system.handlers.event_system import BattleEventSystem


class BattleService:
    """戰鬥服務，整合所有戰鬥系統組件"""
    
    def __init__(
        self,
        config_loader: 'ConfigLoader',
        formula_evaluator: 'FormulaEvaluator'
    ):
        """
        初始化戰鬥服務
        
        Args:
            config_loader: 配置加載器
            formula_evaluator: 公式求值器
        """
        self.config_loader = config_loader
        self.formula_evaluator = formula_evaluator
        
        # 初始化戰鬥處理器
        self._initialize_handlers()
    
    def _initialize_handlers(self) -> None:
        """初始化所有戰鬥處理器"""
        try:
            # 導入處理器類
            from rpg_system.battle_system.handlers.target_selector import TargetSelector
            from rpg_system.battle_system.handlers.damage_handler import DamageHandler
            from rpg_system.battle_system.handlers.passive_trigger_handler import PassiveTriggerHandler
            from rpg_system.battle_system.handlers.effect_applier import EffectApplier
            from rpg_system.battle_system.handlers.event_system import BattleEventSystem
            
            # 創建處理器實例
            self.target_selector = TargetSelector()
            self.damage_handler = DamageHandler(self.formula_evaluator)

            # 創建效果應用器（需要其他處理器作為依賴）
            self.effect_applier = EffectApplier(
                config_loader=self.config_loader,
                formula_evaluator=self.formula_evaluator,
                target_selector=self.target_selector,
                damage_handler=self.damage_handler
            )

            # 創建被動觸發處理器（需要效果應用器作為依賴）
            self.passive_trigger_handler = PassiveTriggerHandler(
                formula_evaluator=self.formula_evaluator,
                effect_applier=self.effect_applier,
                config_loader=self.config_loader
            )

            # 創建事件系統
            self.event_system = BattleEventSystem(
                passive_trigger_handler=self.passive_trigger_handler
            )
            
            logger.info("戰鬥處理器初始化完成")
            
        except Exception as e:
            logger.error(f"初始化戰鬥處理器時發生錯誤: {e}")
            raise
    
    async def create_battle(
        self,
        player_team: List['Combatant'],
        monster_team: List['Combatant'],
        battle_id: Optional[str] = None
    ) -> 'Battle':
        """
        創建戰鬥實例
        
        Args:
            player_team: 玩家隊伍
            monster_team: 怪物隊伍
            battle_id: 戰鬥ID（可選）
            
        Returns:
            戰鬥實例
        """
        try:
            from rpg_system.battle_system.models.battle import Battle
            
            # 創建戰鬥實例
            battle = Battle(
                player_team=player_team,
                monster_team=monster_team,
                battle_id=battle_id
            )
            
            # 設置事件系統
            battle.event_system = self.event_system

            # 初始化戰鬥
            await battle.start(self.config_loader, None)
            
            logger.info(f"戰鬥創建成功: {battle.battle_id}")
            return battle
            
        except Exception as e:
            logger.error(f"創建戰鬥時發生錯誤: {e}")
            raise
    
    async def execute_battle_turn(self, battle: 'Battle') -> Dict[str, Any]:
        """
        執行一個戰鬥回合
        
        Args:
            battle: 戰鬥實例
            
        Returns:
            回合執行結果
        """
        try:
            # 處理回合開始效果
            await battle.process_turn_effects()
            
            # 獲取當前行動者
            current_actor = await battle.get_acting_combatant()
            if not current_actor:
                return {
                    'success': False,
                    'error': '沒有可行動的戰鬥單位'
                }
            
            # 檢查戰鬥是否結束
            if battle.is_battle_over():
                return {
                    'success': True,
                    'battle_over': True,
                    'battle_status': battle.battle_status.value
                }
            
            # 執行行動
            if current_actor.is_player_side:
                # 玩家行動（在自動戰鬥中也使用AI）
                action_result = await self._execute_ai_action(battle, current_actor)
            else:
                # 怪物行動
                action_result = await self._execute_ai_action(battle, current_actor)
            
            # 處理回合結束效果
            await battle.process_turn_end_effects(current_actor)
            
            # 推進到下一個行動者
            await battle.next_turn()
            
            return {
                'success': True,
                'battle_over': battle.is_battle_over(),
                'battle_status': battle.battle_status.value if battle.is_battle_over() else None,
                'action_result': action_result,
                'current_turn': battle.current_turn,
                'acting_combatant': current_actor.instance_id
            }
            
        except Exception as e:
            logger.error(f"執行戰鬥回合時發生錯誤: {e}")
            return {
                'success': False,
                'error': str(e)
            }
    
    async def _execute_ai_action(self, battle: 'Battle', actor: 'Combatant') -> Dict[str, Any]:
        """
        執行AI行動
        
        Args:
            battle: 戰鬥實例
            actor: 行動者
            
        Returns:
            行動結果
        """
        try:
            # 獲取AI決策
            skill_id, target_ids = await actor.get_available_action(battle)
            
            if not skill_id:
                return {
                    'success': False,
                    'error': f'{actor.name} 沒有可用的行動'
                }
            
            # 執行行動
            action_result = await battle.process_action(
                caster=actor,
                skill_id=skill_id,
                target_ids=target_ids,
                effect_applier=self.effect_applier,
                config_loader=self.config_loader
            )
            
            return action_result
            
        except Exception as e:
            logger.error(f"執行AI行動時發生錯誤: {e}")
            return {
                'success': False,
                'error': str(e)
            }
    
    async def run_auto_battle(self, battle: 'Battle', max_turns: int = 100) -> Dict[str, Any]:
        """
        運行自動戰鬥直到結束
        
        Args:
            battle: 戰鬥實例
            max_turns: 最大回合數
            
        Returns:
            戰鬥結果
        """
        try:
            turn_results = []
            
            while not battle.is_battle_over() and battle.current_turn <= max_turns:
                turn_result = await self.execute_battle_turn(battle)
                turn_results.append(turn_result)
                
                if not turn_result['success']:
                    break
                
                if turn_result.get('battle_over', False):
                    break
            
            # 觸發戰鬥結束事件
            if battle.is_battle_over():
                await battle.trigger_event('ON_BATTLE_END', {
                    'final_battle_status': battle.battle_status.value,
                    'total_turns': battle.current_turn,
                    'surviving_players': [c.instance_id for c in battle.player_team if c.is_alive()],
                    'surviving_monsters': [c.instance_id for c in battle.monster_team if c.is_alive()]
                })
            
            return {
                'success': True,
                'battle_status': battle.battle_status.value,
                'total_turns': battle.current_turn,
                'turn_results': turn_results,
                'battle_log': [entry.__dict__ for entry in battle.battle_log]
            }
            
        except Exception as e:
            logger.error(f"運行自動戰鬥時發生錯誤: {e}")
            return {
                'success': False,
                'error': str(e)
            }
    
    def get_battle_summary(self, battle: 'Battle') -> Dict[str, Any]:
        """
        獲取戰鬥摘要
        
        Args:
            battle: 戰鬥實例
            
        Returns:
            戰鬥摘要
        """
        try:
            return {
                'battle_id': battle.battle_id,
                'battle_status': battle.battle_status.value,
                'current_turn': battle.current_turn,
                'player_team': [
                    {
                        'instance_id': c.instance_id,
                        'name': c.name,
                        'current_hp': c.current_hp,
                        'max_hp': c.max_hp,
                        'current_mp': c.current_mp,
                        'max_mp': c.max_mp,
                        'is_alive': c.is_alive()
                    }
                    for c in battle.player_team
                ],
                'monster_team': [
                    {
                        'instance_id': c.instance_id,
                        'name': c.name,
                        'current_hp': c.current_hp,
                        'max_hp': c.max_hp,
                        'current_mp': c.current_mp,
                        'max_mp': c.max_mp,
                        'is_alive': c.is_alive()
                    }
                    for c in battle.monster_team
                ],
                'recent_log_entries': [
                    entry.__dict__ for entry in battle.battle_log[-10:]  # 最近10條日誌
                ]
            }
            
        except Exception as e:
            logger.error(f"獲取戰鬥摘要時發生錯誤: {e}")
            return {
                'error': str(e)
            }
