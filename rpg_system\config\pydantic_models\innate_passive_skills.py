"""
天賦被動技能配置的Pydantic模型（增量式格式）
"""

from typing import List, Optional, Dict
from pydantic import BaseModel, Field

from .base_models import EffectDefinition, TriggerCondition, TargetOverride


class PassiveEffectBlock(BaseModel):
    """被動技能效果塊模型"""
    trigger_condition: TriggerCondition
    target_override: Optional[TargetOverride] = None
    effect_definitions: List[EffectDefinition]


class InnatePassiveSkillConfig(BaseModel):
    """天賦被動技能配置模型（增量式格式）"""
    name: str
    description_template: str
    skill_rarity: int = Field(..., ge=1, le=7)
    base_effects: List[PassiveEffectBlock]
    milestone_effects: Optional[Dict[str, List[PassiveEffectBlock]]] = None
    tags: Optional[List[str]] = None


class InnatePassiveSkillsConfig(BaseModel):
    """天賦被動技能配置文件模型"""
    __root__: Dict[str, InnatePassiveSkillConfig]
    
    def __iter__(self):
        return iter(self.__root__)
    
    def __getitem__(self, item):
        return self.__root__[item]
    
    def get(self, key, default=None):
        return self.__root__.get(key, default)
    
    def keys(self):
        return self.__root__.keys()
    
    def values(self):
        return self.__root__.values()
    
    def items(self):
        return self.__root__.items()
