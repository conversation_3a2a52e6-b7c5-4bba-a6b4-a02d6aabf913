"""
傷害處理器 (DamageHandler)
處理複雜的傷害計算、修正和應用
"""

from typing import Dict, Any, List, Optional, TYPE_CHECKING
import random
from utils.logger import logger

if TYPE_CHECKING:
    from ..models.combatant import Combatant
    from ..models.battle import Battle
    from ...formula_engine.evaluator import FormulaEvaluator
    from ...config.loader import ConfigLoader


class DamageHandler:
    """傷害處理器服務"""
    
    def __init__(self, formula_evaluator: 'FormulaEvaluator'):
        """
        初始化傷害處理器
        
        Args:
            formula_evaluator: 公式求值器
        """
        self.formula_evaluator = formula_evaluator
        
        # 傷害計算常數
        self.DEFENSE_CONSTANT = 100  # 防禦減免計算常數
    
    async def calculate_and_apply_damage(
        self,
        caster: 'Combatant',
        target: 'Combatant',
        damage_effect: Dict[str, Any],
        base_damage_value: float,
        battle_context: 'Battle',
        config_loader: 'ConfigLoader',
        is_crit: Optional[bool] = None,
        skill_tags: Optional[List[str]] = None
    ) -> Dict[str, Any]:
        """
        計算並應用傷害
        
        Args:
            caster: 施法者
            target: 目標
            damage_effect: 傷害效果定義
            base_damage_value: 基礎傷害值
            battle_context: 戰鬥上下文
            config_loader: 配置加載器
            is_crit: 是否暴擊（如果為None則內部計算）
            skill_tags: 技能標籤列表
            
        Returns:
            包含傷害計算詳情的字典
        """
        try:
            # 1. 暴擊判斷
            if is_crit is None:
                is_crit = await self._calculate_crit_chance(caster, damage_effect, battle_context)
            
            # 2. 命中判斷
            was_miss = await self._calculate_miss_chance(caster, target, battle_context)
            
            if was_miss:
                return {
                    'final_damage': 0,
                    'actual_damage_dealt': 0,
                    'was_crit': False,
                    'was_miss': True,
                    'elemental_advantage': 1.0,
                    'damage_breakdown': {
                        'base_damage': base_damage_value,
                        'crit_multiplier': 1.0,
                        'defense_reduction': 0.0,
                        'elemental_multiplier': 1.0
                    }
                }
            
            # 3. 暴擊倍率計算
            crit_multiplier = await self._calculate_crit_multiplier(caster, is_crit)
            
            # 4. 屬性克制計算
            elemental_multiplier = await self._calculate_elemental_advantage(
                caster, target, damage_effect, skill_tags or []
            )
            
            # 5. 防禦減免計算
            defense_reduction = await self._calculate_defense_reduction(
                target, damage_effect
            )
            
            # 6. 應用修正器
            modifier_multiplier = await self._apply_damage_modifiers(
                caster, target, damage_effect, base_damage_value, battle_context
            )
            
            # 7. 最終傷害計算
            final_damage = (
                base_damage_value * 
                crit_multiplier * 
                elemental_multiplier * 
                modifier_multiplier * 
                (1 - defense_reduction)
            )
            
            # 確保傷害不為負數
            final_damage = max(0, final_damage)
            
            # 8. 應用傷害到目標
            actual_damage_dealt = await target.take_damage(
                final_damage, 
                damage_effect.get('damage_type', 'PHYSICAL'), 
                is_crit, 
                battle_context
            )
            
            return {
                'final_damage': final_damage,
                'actual_damage_dealt': actual_damage_dealt,
                'was_crit': is_crit,
                'was_miss': False,
                'elemental_advantage': elemental_multiplier,
                'damage_breakdown': {
                    'base_damage': base_damage_value,
                    'crit_multiplier': crit_multiplier,
                    'defense_reduction': defense_reduction,
                    'elemental_multiplier': elemental_multiplier,
                    'modifier_multiplier': modifier_multiplier
                }
            }
            
        except Exception as e:
            logger.error(f"傷害計算錯誤: {e}")
            return {
                'final_damage': 0,
                'actual_damage_dealt': 0,
                'was_crit': False,
                'was_miss': True,
                'elemental_advantage': 1.0,
                'damage_breakdown': {}
            }
    
    async def _calculate_crit_chance(
        self,
        caster: 'Combatant',
        damage_effect: Dict[str, Any],
        battle_context: 'Battle'
    ) -> bool:
        """
        計算暴擊機率
        
        Args:
            caster: 施法者
            damage_effect: 傷害效果定義
            battle_context: 戰鬥上下文
            
        Returns:
            是否暴擊
        """
        # 檢查效果是否允許暴擊
        can_crit = damage_effect.get('can_crit', True)
        if not can_crit:
            return False
        
        # 獲取暴擊率
        crit_rate = caster.current_stats.get('crit_rate', 0.05)
        
        # 使用戰鬥上下文的隨機數生成器
        if hasattr(battle_context, '_rng'):
            roll = battle_context._rng.random()
        else:
            roll = random.random()
        
        return roll < crit_rate
    
    async def _calculate_miss_chance(
        self,
        caster: 'Combatant',
        target: 'Combatant',
        battle_context: 'Battle'
    ) -> bool:
        """
        計算命中/閃避
        
        Args:
            caster: 施法者
            target: 目標
            battle_context: 戰鬥上下文
            
        Returns:
            是否未命中
        """
        accuracy = caster.current_stats.get('accuracy', 0.95)
        evasion = target.current_stats.get('evasion', 0.05)
        
        hit_chance = max(0.05, min(0.95, accuracy - evasion))  # 限制在5%-95%之間
        
        # 使用戰鬥上下文的隨機數生成器
        if hasattr(battle_context, '_rng'):
            roll = battle_context._rng.random()
        else:
            roll = random.random()
        
        return roll > hit_chance
    
    async def _calculate_crit_multiplier(self, caster: 'Combatant', is_crit: bool) -> float:
        """
        計算暴擊倍率
        
        Args:
            caster: 施法者
            is_crit: 是否暴擊
            
        Returns:
            暴擊倍率
        """
        if not is_crit:
            return 1.0
        
        return caster.current_stats.get('crit_dmg_multiplier', 1.5)
    
    async def _calculate_elemental_advantage(
        self,
        caster: 'Combatant',
        target: 'Combatant',
        damage_effect: Dict[str, Any],
        skill_tags: List[str]
    ) -> float:
        """
        計算屬性克制倍率
        
        Args:
            caster: 施法者
            target: 目標
            damage_effect: 傷害效果定義
            skill_tags: 技能標籤列表
            
        Returns:
            屬性克制倍率
        """
        # 簡化實現，後續可以根據需要擴展
        # 這裡可以根據施法者和目標的屬性、技能標籤等計算克制關係
        return 1.0
    
    async def _calculate_defense_reduction(
        self,
        target: 'Combatant',
        damage_effect: Dict[str, Any]
    ) -> float:
        """
        計算防禦減免
        
        Args:
            target: 目標
            damage_effect: 傷害效果定義
            
        Returns:
            防禦減免比例（0.0-1.0）
        """
        damage_type = damage_effect.get('damage_type', 'PHYSICAL')
        
        if damage_type == 'PHYSICAL':
            defense = target.current_stats.get('pdef', 0)
        elif damage_type == 'MAGICAL':
            defense = target.current_stats.get('mdef', 0)
        elif damage_type == 'TRUE_DAMAGE':
            return 0.0  # 真實傷害無視防禦
        else:
            defense = 0
        
        # 防禦減免公式: defense / (defense + DEFENSE_CONSTANT)
        # 這確保了防禦減免永遠不會達到100%
        if defense <= 0:
            return 0.0
        
        reduction = defense / (defense + self.DEFENSE_CONSTANT)
        return min(0.9, reduction)  # 最大減免90%
    
    async def _apply_damage_modifiers(
        self,
        caster: 'Combatant',
        target: 'Combatant',
        damage_effect: Dict[str, Any],
        base_damage: float,
        battle_context: 'Battle'
    ) -> float:
        """
        應用傷害修正器
        
        Args:
            caster: 施法者
            target: 目標
            damage_effect: 傷害效果定義
            base_damage: 基礎傷害
            battle_context: 戰鬥上下文
            
        Returns:
            修正器倍率
        """
        modifiers = damage_effect.get('modifiers', [])
        if not modifiers:
            return 1.0
        
        total_multiplier = 1.0
        
        for modifier in modifiers:
            modifier_type = modifier.get('modifier_type')
            
            if modifier_type == 'SCALING_MODIFIER':
                multiplier = await self._apply_scaling_modifier(
                    modifier, caster, target, battle_context
                )
                total_multiplier *= multiplier
            elif modifier_type == 'CONDITIONAL_BOOST':
                multiplier = await self._apply_conditional_boost(
                    modifier, caster, target, battle_context
                )
                total_multiplier *= multiplier
            else:
                logger.warning(f"未知的修正器類型: {modifier_type}")
        
        return total_multiplier
    
    async def _apply_scaling_modifier(
        self,
        modifier: Dict[str, Any],
        caster: 'Combatant',
        target: 'Combatant',
        battle_context: 'Battle'
    ) -> float:
        """
        應用縮放修正器
        
        Args:
            modifier: 修正器定義
            caster: 施法者
            target: 目標
            battle_context: 戰鬥上下文
            
        Returns:
            修正器倍率
        """
        scaling_source = modifier.get('scaling_source', '')
        scaling_factor = modifier.get('scaling_factor', 0.0)
        
        # 解析縮放來源
        if scaling_source.startswith('stat:'):
            stat_name = scaling_source[5:]  # 移除 'stat:' 前綴
            stat_value = caster.current_stats.get(stat_name, 0)
            return 1.0 + (stat_value * scaling_factor)
        elif scaling_source.startswith('target_stat:'):
            stat_name = scaling_source[12:]  # 移除 'target_stat:' 前綴
            stat_value = target.current_stats.get(stat_name, 0)
            return 1.0 + (stat_value * scaling_factor)
        else:
            logger.warning(f"未知的縮放來源: {scaling_source}")
            return 1.0
    
    async def _apply_conditional_boost(
        self,
        modifier: Dict[str, Any],
        caster: 'Combatant',
        target: 'Combatant',
        battle_context: 'Battle'
    ) -> float:
        """
        應用條件加成修正器
        
        Args:
            modifier: 修正器定義
            caster: 施法者
            target: 目標
            battle_context: 戰鬥上下文
            
        Returns:
            修正器倍率
        """
        condition_formula = modifier.get('condition_formula', '1')
        boost_value = modifier.get('boost_value', 0.0)
        
        # 準備條件評估上下文
        context_vars = {
            'caster_current_hp_percent': caster.current_hp / max(caster.max_hp, 1),
            'target_current_hp_percent': target.current_hp / max(target.max_hp, 1),
            'caster_stat_patk': caster.current_stats.get('patk', 0),
            'caster_stat_matk': caster.current_stats.get('matk', 0),
            'target_stat_pdef': target.current_stats.get('pdef', 0),
            'target_stat_mdef': target.current_stats.get('mdef', 0),
        }
        
        # 評估條件
        condition_met = await self.formula_evaluator.evaluate(condition_formula, context_vars)
        
        if condition_met:
            return 1.0 + boost_value
        else:
            return 1.0
