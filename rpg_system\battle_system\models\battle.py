"""
戰鬥實例領域模型
"""

from dataclasses import dataclass, field
from typing import List, Optional, Dict, Any, TYPE_CHECKING
from enum import Enum
import uuid
from utils.logger import logger

from .combatant import Combatant
from .battle_log import BattleLogEntry
from .enums import BattleStatus, EventType

if TYPE_CHECKING:
    from ..handlers.event_system import BattleEventSystem


@dataclass
class Battle:
    """戰鬥實例領域模型"""
    
    # 基本屬性
    battle_id: str = field(default_factory=lambda: str(uuid.uuid4()))
    player_team: List[Combatant] = field(default_factory=list)
    monster_team: List[Combatant] = field(default_factory=list)
    current_turn: int = 0
    battle_log: List[BattleLogEntry] = field(default_factory=list)
    battle_status: BattleStatus = BattleStatus.PENDING
    rng_seed: Optional[int] = None
    combatant_queue: List[str] = field(default_factory=list)  # Combatant instance_ids
    
    # 戰鬥配置
    max_turns: int = 100  # 最大回合數，防止無限戰鬥

    # 事件系統
    event_system: Optional['BattleEventSystem'] = None
    
    async def start(self, config_loader=None, attribute_calculator=None) -> None:
        """
        初始化戰鬥
        計算所有Combatant的初始屬性，決定先手並生成初始行動順序隊列
        """
        if self.battle_status != BattleStatus.PENDING:
            raise ValueError("戰鬥已經開始或結束")

        # 初始化所有戰鬥單位的屬性
        all_combatants = self.player_team + self.monster_team
        for combatant in all_combatants:
            # 這裡會調用 combatant.calculate_final_stats()
            # 具體實現在 Combatant 類中
            if config_loader and attribute_calculator:
                await combatant.calculate_final_stats(config_loader, attribute_calculator)

        # 生成初始行動順序隊列（按速度排序）
        await self._generate_action_queue()

        # 設置戰鬥狀態
        self.battle_status = BattleStatus.IN_PROGRESS
        self.current_turn = 1

        # 記錄戰鬥開始日誌
        self.add_log_entry("戰鬥開始", "ENVIRONMENT", None, None, {})

        # 觸發戰鬥開始事件
        await self.trigger_event(EventType.ON_BATTLE_START.value, {
            'player_team': [c.instance_id for c in self.player_team],
            'monster_team': [c.instance_id for c in self.monster_team],
            'all_combatants': [c.instance_id for c in all_combatants]
        })
    
    async def next_turn(self) -> None:
        """
        推進到下一回合或下一行動單位
        處理回合開始/結束邏輯
        """
        if self.battle_status != BattleStatus.IN_PROGRESS:
            return
        
        # 移除當前行動單位
        if self.combatant_queue:
            self.combatant_queue.pop(0)
        
        # 如果隊列為空，進入下一回合
        if not self.combatant_queue:
            self.current_turn += 1
            
            # 檢查是否達到最大回合數
            if self.current_turn > self.max_turns:
                self.battle_status = BattleStatus.DRAW
                self.add_log_entry("戰鬥因達到最大回合數而結束", "ENVIRONMENT", None, None, {})
                return
            
            # 重新生成行動隊列
            await self._generate_action_queue()
    
    async def get_acting_combatant(self) -> Optional[Combatant]:
        """
        從行動隊列中獲取當前行動的單位
        
        Returns:
            當前行動的Combatant，如果隊列為空則返回None
        """
        if not self.combatant_queue:
            return None
        
        acting_id = self.combatant_queue[0]
        
        # 在所有戰鬥單位中查找
        all_combatants = self.player_team + self.monster_team
        for combatant in all_combatants:
            if combatant.instance_id == acting_id and combatant.is_alive():
                return combatant
        
        # 如果找不到或已死亡，移除並嘗試下一個
        self.combatant_queue.pop(0)
        return await self.get_acting_combatant()
    
    async def process_action(
        self,
        caster: Combatant,
        skill_id: str,
        target_ids: Optional[List[str]] = None,
        effect_applier=None,
        config_loader=None
    ) -> Dict[str, Any]:
        """
        核心方法，處理一個單位的行動

        Args:
            caster: 施法者
            skill_id: 技能ID
            target_ids: 目標ID列表（可選，由TargetSelector自動選擇）
            effect_applier: 效果應用器實例
            config_loader: 配置加載器實例

        Returns:
            行動處理結果
        """
        try:
            # 1. 獲取技能實例
            skill_instance = caster.get_skill_instance(skill_id)
            if not skill_instance:
                error_msg = f"施法者 {caster.name} 沒有技能 {skill_id}"
                self.add_log_entry(error_msg, "ERROR", caster.instance_id, [], {})
                return {'success': False, 'error': error_msg}

            # 2. 檢查技能是否可用
            if not skill_instance.is_usable(caster.current_mp):
                error_msg = f"技能 {skill_id} 不可用（冷卻中或MP不足）"
                self.add_log_entry(error_msg, "SKILL_UNUSABLE", caster.instance_id, [], {})
                return {'success': False, 'error': error_msg}

            # 3. 獲取技能定義
            if not config_loader:
                error_msg = "缺少配置加載器"
                return {'success': False, 'error': error_msg}

            skill_definition = skill_instance.get_definition(config_loader.get_all_configs())
            if not skill_definition:
                error_msg = f"找不到技能 {skill_id} 的定義"
                self.add_log_entry(error_msg, "ERROR", caster.instance_id, [], {})
                return {'success': False, 'error': error_msg}

            # 4. 確定目標
            if target_ids:
                # 使用指定的目標
                targets = []
                for target_id in target_ids:
                    target = self.get_combatant_by_id(target_id)
                    if target and target.is_alive():
                        targets.append(target)
            else:
                # 使用技能的默認目標選擇邏輯
                targets = self._select_skill_targets(caster, skill_definition, effect_applier)

            if not targets:
                error_msg = f"沒有有效目標可供技能 {skill_id} 使用"
                self.add_log_entry(error_msg, "NO_TARGETS", caster.instance_id, [], {})
                return {'success': False, 'error': error_msg}

            # 5. 應用技能效果
            if not effect_applier:
                error_msg = "缺少效果應用器"
                return {'success': False, 'error': error_msg}

            effect_results = effect_applier.apply_skill_effects(
                caster=caster,
                targets=targets,
                skill_instance=skill_instance,
                battle_context=self
            )

            # 6. 消耗資源和設置冷卻
            await self._consume_skill_resources(caster, skill_instance, skill_definition)

            # 7. 觸發技能執行事件
            await self._trigger_skill_execution_event(caster, skill_id, skill_definition, targets, effect_results)

            # 8. 記錄戰鬥日誌
            target_ids_for_log = [t.instance_id for t in targets]
            self.add_log_entry(
                f"{caster.name} 使用了 {skill_definition.get('name', skill_id)}",
                "SKILL_USE",
                caster.instance_id,
                target_ids_for_log,
                {
                    'skill_id': skill_id,
                    'effect_results': effect_results
                }
            )

            # 8. 移除當前行動者從隊列
            if self.combatant_queue and self.combatant_queue[0] == caster.instance_id:
                self.combatant_queue.pop(0)

            return {
                'success': True,
                'skill_id': skill_id,
                'caster_id': caster.instance_id,
                'target_ids': target_ids_for_log,
                'effect_results': effect_results
            }

        except Exception as e:
            error_msg = f"處理行動時發生錯誤: {e}"
            logger.error(error_msg)
            self.add_log_entry(error_msg, "ERROR", caster.instance_id, [], {})
            return {'success': False, 'error': error_msg}
    
    async def check_win_condition(self) -> None:
        """檢查戰鬥是否結束"""
        if self.battle_status != BattleStatus.IN_PROGRESS:
            return
        
        # 檢查玩家隊伍是否全部死亡
        player_alive = any(combatant.is_alive() for combatant in self.player_team)
        monster_alive = any(combatant.is_alive() for combatant in self.monster_team)
        
        if not player_alive and not monster_alive:
            self.battle_status = BattleStatus.DRAW
            self.add_log_entry("雙方同歸於盡", "ENVIRONMENT", None, None, {})
        elif not player_alive:
            self.battle_status = BattleStatus.MONSTER_WIN
            self.add_log_entry("怪物獲勝", "ENVIRONMENT", None, None, {})
        elif not monster_alive:
            self.battle_status = BattleStatus.PLAYER_WIN
            self.add_log_entry("玩家獲勝", "ENVIRONMENT", None, None, {})

    def is_battle_over(self) -> bool:
        """
        檢查戰鬥是否結束

        Returns:
            戰鬥是否結束
        """
        return self.battle_status != BattleStatus.IN_PROGRESS
    
    def add_log_entry(self, message: str, action_type: str, actor_id: Optional[str], 
                     target_ids: Optional[List[str]], details: Dict[str, Any]) -> None:
        """
        記錄戰鬥日誌
        
        Args:
            message: 日誌消息
            action_type: 行動類型
            actor_id: 行動者ID
            target_ids: 目標ID列表
            details: 詳細信息
        """
        log_entry = BattleLogEntry(
            turn_number=self.current_turn,
            action_type=action_type,
            actor_id=actor_id,
            target_ids=target_ids or [],
            message=message,
            details=details
        )
        self.battle_log.append(log_entry)

    async def trigger_event(self, event_type: str, event_data: Dict[str, Any]) -> List[Dict[str, Any]]:
        """
        觸發戰鬥事件

        Args:
            event_type: 事件類型
            event_data: 事件數據

        Returns:
            事件處理結果列表
        """
        if self.event_system:
            return await self.event_system.trigger_event(event_type, event_data, self)
        return []

    async def _trigger_skill_execution_event(
        self,
        caster: Combatant,
        skill_id: str,
        skill_definition: Dict[str, Any],
        targets: List[Combatant],
        effect_results: List[Dict[str, Any]]
    ) -> None:
        """
        觸發技能執行事件

        Args:
            caster: 施法者
            skill_id: 技能ID
            skill_definition: 技能定義
            targets: 目標列表
            effect_results: 效果結果列表
        """
        try:
            # 判斷技能類型
            skill_type = skill_definition.get('type', 'ACTIVE')

            # 準備事件數據
            event_data = {
                'caster_id': caster.instance_id,
                'skill_id': skill_id,
                'skill_name': skill_definition.get('name', skill_id),
                'target_ids': [t.instance_id for t in targets],
                'effect_results': effect_results,
                'skill_type': skill_type
            }

            # 根據技能類型觸發不同事件
            if skill_type == 'PRIMARY_ATTACK':
                await self.trigger_event(EventType.ON_PRIMARY_ATTACK_EXECUTED.value, event_data)
            else:
                await self.trigger_event(EventType.ON_ACTIVE_SKILL_EXECUTED.value, event_data)

        except Exception as e:
            logger.error(f"觸發技能執行事件時發生錯誤: {e}")

    async def trigger_death_event(self, dead_combatant: Combatant) -> None:
        """
        觸發死亡事件

        Args:
            dead_combatant: 死亡的戰鬥單位
        """
        try:
            # 判斷死亡單位的陣營
            is_player_team = dead_combatant in self.player_team

            # 準備事件數據
            death_event_data = {
                'dead_combatant_id': dead_combatant.instance_id,
                'dead_combatant_name': dead_combatant.name,
                'was_player_team': is_player_team,
                'remaining_allies': [
                    c.instance_id for c in (self.player_team if is_player_team else self.monster_team)
                    if c.is_alive()
                ],
                'remaining_enemies': [
                    c.instance_id for c in (self.monster_team if is_player_team else self.player_team)
                    if c.is_alive()
                ]
            }

            # 觸發通用死亡事件
            await self.trigger_event(EventType.ON_COMBATANT_DEATH.value, death_event_data)

            # 觸發陣營特定死亡事件
            if is_player_team:
                await self.trigger_event(EventType.ON_ALLY_DEATH.value, death_event_data)
            else:
                await self.trigger_event(EventType.ON_ENEMY_DEATH.value, death_event_data)

        except Exception as e:
            logger.error(f"觸發死亡事件時發生錯誤: {e}")
    
    async def _generate_action_queue(self) -> None:
        """生成行動順序隊列（按速度排序）"""
        all_combatants = self.player_team + self.monster_team
        alive_combatants = [c for c in all_combatants if c.is_alive()]
        
        # 按速度排序（速度高的先行動）
        alive_combatants.sort(key=lambda c: c.current_stats.get('spd', 0), reverse=True)
        
        # 生成隊列
        self.combatant_queue = [c.instance_id for c in alive_combatants]
    
    def get_combatant_by_id(self, instance_id: str) -> Optional[Combatant]:
        """
        根據ID獲取戰鬥單位
        
        Args:
            instance_id: 戰鬥單位實例ID
            
        Returns:
            對應的Combatant，如果找不到則返回None
        """
        all_combatants = self.player_team + self.monster_team
        for combatant in all_combatants:
            if combatant.instance_id == instance_id:
                return combatant
        return None

    def get_all_alive_enemies_of(self, combatant: Combatant) -> List[Combatant]:
        """
        獲取指定戰鬥單位的所有存活敵人

        Args:
            combatant: 指定的戰鬥單位

        Returns:
            敵方存活戰鬥單位列表
        """
        if combatant.is_player_side:
            # 玩家方的敵人是怪物方
            return [c for c in self.monster_team if c.is_alive()]
        else:
            # 怪物方的敵人是玩家方
            return [c for c in self.player_team if c.is_alive()]

    def get_all_alive_allies_of(self, combatant: Combatant) -> List[Combatant]:
        """
        獲取指定戰鬥單位的所有存活盟友（包括自己）

        Args:
            combatant: 指定的戰鬥單位

        Returns:
            己方存活戰鬥單位列表
        """
        if combatant.is_player_side:
            # 玩家方的盟友是玩家方
            return [c for c in self.player_team if c.is_alive()]
        else:
            # 怪物方的盟友是怪物方
            return [c for c in self.monster_team if c.is_alive()]

    def get_all_alive_combatants(self) -> List[Combatant]:
        """
        獲取所有存活的戰鬥單位

        Returns:
            所有存活戰鬥單位列表
        """
        all_combatants = self.player_team + self.monster_team
        return [c for c in all_combatants if c.is_alive()]

    async def _select_skill_targets(self, caster: Combatant, skill_definition: Dict[str, Any], effect_applier) -> List[Combatant]:
        """
        選擇技能目標

        Args:
            caster: 施法者
            skill_definition: 技能定義
            effect_applier: 效果應用器（包含目標選擇器）

        Returns:
            目標列表
        """
        try:
            # 獲取技能的目標邏輯
            target_logic = skill_definition.get('target_logic', {})

            if not target_logic:
                # 默認目標邏輯：攻擊敵人
                return self.get_all_alive_enemies_of(caster)[:1]  # 選擇第一個敵人

            # 使用目標選擇器
            if hasattr(effect_applier, 'target_selector'):
                return await effect_applier.target_selector.select_targets(
                    caster, target_logic, self,
                    effect_applier.formula_evaluator, effect_applier.config_loader
                )
            else:
                # 降級處理
                base_pool = target_logic.get('base_pool', 'ENEMIES')
                if base_pool == 'ENEMIES':
                    return self.get_all_alive_enemies_of(caster)[:1]
                elif base_pool == 'ALLIES':
                    return self.get_all_alive_allies_of(caster)[:1]
                elif base_pool == 'SELF':
                    return [caster] if caster.is_alive() else []
                else:
                    return self.get_all_alive_enemies_of(caster)[:1]

        except Exception as e:
            logger.error(f"選擇技能目標時發生錯誤: {e}")
            # 降級到默認行為
            return self.get_all_alive_enemies_of(caster)[:1]

    async def _consume_skill_resources(self, caster: Combatant, skill_instance, skill_definition: Dict[str, Any]) -> None:
        """
        消耗技能資源並設置冷卻

        Args:
            caster: 施法者
            skill_instance: 技能實例
            skill_definition: 技能定義
        """
        try:
            # 消耗MP
            mp_cost = skill_definition.get('mp_cost', 0)
            if mp_cost > 0:
                caster.consume_mp(mp_cost)

            # 設置冷卻
            cooldown = skill_definition.get('cooldown', 0)
            if cooldown > 0:
                skill_instance.current_cooldown = cooldown

        except Exception as e:
            logger.error(f"消耗技能資源時發生錯誤: {e}")

    async def process_turn_effects(self) -> None:
        """
        處理回合效果（回合開始/結束時的狀態效果、被動觸發等）
        """
        try:
            # 獲取當前行動者
            acting_combatant = await self.get_acting_combatant()

            # 觸發回合開始事件
            if acting_combatant:
                await self.trigger_event(EventType.ON_TURN_START.value, {
                    'current_turn_number': self.current_turn,
                    'acting_combatant_id': acting_combatant.instance_id,
                    'all_combatants_on_field': [c.instance_id for c in self.get_all_alive_combatants()]
                })

            # 處理所有存活戰鬥單位的回合效果
            for combatant in self.get_all_alive_combatants():
                await combatant.apply_turn_start_effects(self)

        except Exception as e:
            logger.error(f"處理回合效果時發生錯誤: {e}")

    async def process_turn_end_effects(self, completed_combatant: Combatant) -> None:
        """
        處理回合結束效果

        Args:
            completed_combatant: 剛完成行動的戰鬥單位
        """
        try:
            # 觸發回合結束事件
            await self.trigger_event(EventType.ON_TURN_END.value, {
                'current_turn_number': self.current_turn,
                'completed_action_combatant_id': completed_combatant.instance_id,
                'all_combatants_on_field': [c.instance_id for c in self.get_all_alive_combatants()]
            })

            # 處理所有存活戰鬥單位的回合結束效果
            for combatant in self.get_all_alive_combatants():
                await combatant.apply_turn_end_effects(self)

        except Exception as e:
            logger.error(f"處理回合結束效果時發生錯誤: {e}")

    async def auto_battle_step(self, effect_applier=None, config_loader=None) -> Dict[str, Any]:
        """
        自動戰鬥步驟（AI決策並執行行動）

        Args:
            effect_applier: 效果應用器實例
            config_loader: 配置加載器實例

        Returns:
            行動結果
        """
        try:
            # 檢查戰鬥是否結束
            await self.check_win_condition()
            if self.battle_status != BattleStatus.IN_PROGRESS:
                return {'battle_ended': True, 'status': self.battle_status.value}

            # 獲取當前行動者
            acting_combatant = await self.get_acting_combatant()
            if not acting_combatant:
                # 沒有行動者，進入下一回合
                await self.next_turn()
                await self.process_turn_effects()
                return {'next_turn': True, 'current_turn': self.current_turn}

            # AI決策：選擇技能
            skill_id, _ = acting_combatant.get_available_action(self)
            if not skill_id:
                # 沒有可用技能，跳過行動
                self.combatant_queue.pop(0) if self.combatant_queue else None
                return {'skipped': True, 'actor': acting_combatant.name}

            # 執行行動
            action_result = await self.process_action(
                caster=acting_combatant,
                skill_id=skill_id,
                target_ids=None,  # 讓系統自動選擇目標
                effect_applier=effect_applier,
                config_loader=config_loader
            )

            # 檢查戰鬥是否結束
            await self.check_win_condition()

            return action_result

        except Exception as e:
            error_msg = f"自動戰鬥步驟錯誤: {e}"
            logger.error(error_msg)
            return {'error': error_msg}
