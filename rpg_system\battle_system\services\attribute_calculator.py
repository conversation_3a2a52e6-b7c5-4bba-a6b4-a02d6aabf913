"""
屬性計算器服務

負責根據基礎配置、RPG等級、培養星級和星級效果來計算戰鬥單位的完整屬性。
"""

import logging
from typing import Dict, Literal, TYPE_CHECKING

if TYPE_CHECKING:
    from rpg_system.config.loader import ConfigLoader

logger = logging.getLogger(__name__)


class AttributeCalculator:
    """
    屬性計算器服務
    
    負責計算戰鬥單位的最終屬性，包括：
    - 基礎屬性
    - RPG等級成長
    - 星級效果加成
    """
    
    def __init__(self):
        """初始化屬性計算器"""
        pass
    
    async def calculate_attributes(
        self,
        combatant_definition_id: str,
        combatant_type: Literal["CARD", "MONSTER"],
        rpg_level: int,
        star_level: int,
        config_loader: 'ConfigLoader'
    ) -> Dict[str, float]:
        """
        計算戰鬥單位的最終屬性
        
        Args:
            combatant_definition_id: 卡牌ID或怪物ID
            combatant_type: "CARD" 或 "MONSTER"
            rpg_level: RPG等級
            star_level: 培養星級 (0-35)
            config_loader: 配置加載器實例
            
        Returns:
            包含所有計算後戰鬥屬性的字典
        """
        try:
            # 步驟1: 獲取基礎配置
            base_stats_config, growth_config, star_effects_key = self._get_base_configuration(
                combatant_definition_id, combatant_type, config_loader
            )
            
            if not base_stats_config:
                logger.error(f"無法獲取 {combatant_type} {combatant_definition_id} 的基礎配置")
                return {}
            
            # 初始化計算結果，從基礎屬性開始
            calculated_attributes = self._initialize_attributes_from_base_stats(base_stats_config)
            
            # 步驟2: 計算RPG等級成長 (僅對卡牌)
            if combatant_type == "CARD" and growth_config:
                self._apply_rpg_level_growth(calculated_attributes, growth_config, rpg_level)
            
            # 步驟3: 應用星級效果 (僅對卡牌)
            if combatant_type == "CARD" and star_effects_key:
                self._apply_star_level_effects(
                    calculated_attributes, base_stats_config, star_effects_key, 
                    star_level, config_loader
                )
            
            # 步驟4: 最終處理
            self._finalize_attributes(calculated_attributes)
            
            logger.debug(f"計算完成 {combatant_type} {combatant_definition_id} 屬性: {calculated_attributes}")
            return calculated_attributes
            
        except Exception as e:
            logger.error(f"計算屬性時發生錯誤: {e}")
            return {}
    
    def _get_base_configuration(
        self,
        combatant_definition_id: str,
        combatant_type: str,
        config_loader: 'ConfigLoader'
    ) -> tuple:
        """
        獲取基礎配置
        
        Returns:
            (base_stats_config, growth_config, star_effects_key)
        """
        if combatant_type == "CARD":
            card_config = config_loader.get_card_config(combatant_definition_id)
            if not card_config:
                return None, None, None
            
            return (
                card_config.base_stats,
                card_config.growth_per_rpg_level,
                getattr(card_config, 'star_level_effects_key', None)
            )
            
        elif combatant_type == "MONSTER":
            monster_config = config_loader.get_monster_config(combatant_definition_id)
            if not monster_config:
                return None, None, None
            
            # 怪物配置直接包含屬性，無成長和星級效果
            return monster_config, None, None
            
        else:
            logger.error(f"未知的戰鬥單位類型: {combatant_type}")
            return None, None, None
    
    def _initialize_attributes_from_base_stats(self, base_stats_config) -> Dict[str, float]:
        """
        從基礎屬性初始化計算結果
        """
        calculated_attributes = {}
        
        # 核心屬性列表
        core_attributes = [
            'max_hp', 'max_mp', 'mp_regen_per_turn',
            'patk', 'pdef', 'matk', 'mdef', 'spd',
            'crit_rate', 'crit_dmg_multiplier', 'accuracy', 'evasion'
        ]
        
        # 從基礎配置複製屬性
        if hasattr(base_stats_config, '__dict__'):
            # Pydantic模型
            for attr_name in core_attributes:
                value = getattr(base_stats_config, attr_name, 0.0)
                calculated_attributes[attr_name] = float(value)
        elif isinstance(base_stats_config, dict):
            # 字典格式
            for attr_name in core_attributes:
                value = base_stats_config.get(attr_name, 0.0)
                calculated_attributes[attr_name] = float(value)
        
        return calculated_attributes
    
    def _apply_rpg_level_growth(
        self,
        calculated_attributes: Dict[str, float],
        growth_config,
        rpg_level: int
    ) -> None:
        """
        應用RPG等級成長
        """
        # 有效等級 = 當前等級 - 1 (基礎屬性是1級時的屬性)
        effective_rpg_level = max(0, rpg_level - 1)
        
        if effective_rpg_level <= 0:
            return
        
        # 成長屬性映射
        growth_mappings = {
            'hp': 'max_hp',
            'mp': 'max_mp',
            'patk': 'patk',
            'pdef': 'pdef',
            'matk': 'matk',
            'mdef': 'mdef',
            'spd': 'spd'
        }
        
        # 應用成長
        if hasattr(growth_config, '__dict__'):
            # Pydantic模型
            for growth_attr, target_attr in growth_mappings.items():
                growth_value = getattr(growth_config, growth_attr, 0.0)
                if growth_value > 0:
                    calculated_attributes[target_attr] += growth_value * effective_rpg_level
        elif isinstance(growth_config, dict):
            # 字典格式
            for growth_attr, target_attr in growth_mappings.items():
                growth_value = growth_config.get(growth_attr, 0.0)
                if growth_value > 0:
                    calculated_attributes[target_attr] += growth_value * effective_rpg_level
    
    def _apply_star_level_effects(
        self,
        calculated_attributes: Dict[str, float],
        base_stats_config,
        star_effects_key: str,
        star_level: int,
        config_loader: 'ConfigLoader'
    ) -> None:
        """
        應用星級效果
        """
        try:
            # 獲取星級效果配置
            star_effects_config = config_loader.get_star_level_effects_config(star_effects_key)
            if not star_effects_config:
                logger.warning(f"找不到星級效果配置: {star_effects_key}")
                return
            
            # 找到適用的星級效果 (不大於當前星級的最大檔位)
            applicable_effects = []
            
            if hasattr(star_effects_config, 'effects') and star_effects_config.effects:
                for effect in star_effects_config.effects:
                    required_star_level = getattr(effect, 'required_star_level', 0)
                    if required_star_level <= star_level:
                        applicable_effects.append(effect)
            
            # 按星級要求排序，應用所有符合條件的效果
            applicable_effects.sort(key=lambda x: getattr(x, 'required_star_level', 0))
            
            for effect in applicable_effects:
                self._apply_single_star_effect(calculated_attributes, base_stats_config, effect)
                
        except Exception as e:
            logger.error(f"應用星級效果時發生錯誤: {e}")
    
    def _apply_single_star_effect(
        self,
        calculated_attributes: Dict[str, float],
        base_stats_config,
        effect
    ) -> None:
        """
        應用單個星級效果
        """
        # 應用扁平屬性加成
        if hasattr(effect, 'additional_stats_flat') and effect.additional_stats_flat:
            flat_stats = effect.additional_stats_flat
            if hasattr(flat_stats, '__dict__'):
                for stat_name, value in flat_stats.__dict__.items():
                    if value and value > 0:
                        calculated_attributes[stat_name] = calculated_attributes.get(stat_name, 0) + value
            elif isinstance(flat_stats, dict):
                for stat_name, value in flat_stats.items():
                    if value and value > 0:
                        calculated_attributes[stat_name] = calculated_attributes.get(stat_name, 0) + value
        
        # 應用百分比屬性加成 (基於基礎屬性)
        if hasattr(effect, 'additional_stats_percent') and effect.additional_stats_percent:
            percent_stats = effect.additional_stats_percent
            if hasattr(percent_stats, '__dict__'):
                for stat_name, percent_value in percent_stats.__dict__.items():
                    if percent_value and percent_value > 0:
                        base_value = self._get_base_stat_value(base_stats_config, stat_name)
                        calculated_attributes[stat_name] += base_value * percent_value
            elif isinstance(percent_stats, dict):
                for stat_name, percent_value in percent_stats.items():
                    if percent_value and percent_value > 0:
                        base_value = self._get_base_stat_value(base_stats_config, stat_name)
                        calculated_attributes[stat_name] += base_value * percent_value
    
    def _get_base_stat_value(self, base_stats_config, stat_name: str) -> float:
        """
        獲取基礎屬性值
        """
        if hasattr(base_stats_config, stat_name):
            return float(getattr(base_stats_config, stat_name, 0.0))
        elif isinstance(base_stats_config, dict):
            return float(base_stats_config.get(stat_name, 0.0))
        return 0.0
    
    def _finalize_attributes(self, calculated_attributes: Dict[str, float]) -> None:
        """
        最終處理屬性
        """
        # 確保所有核心屬性都存在
        core_attributes_defaults = {
            'max_hp': 100.0,
            'max_mp': 50.0,
            'mp_regen_per_turn': 0.0,
            'patk': 10.0,
            'pdef': 10.0,
            'matk': 10.0,
            'mdef': 10.0,
            'spd': 10.0,
            'crit_rate': 0.05,
            'crit_dmg_multiplier': 1.5,
            'accuracy': 0.95,
            'evasion': 0.05
        }
        
        for attr_name, default_value in core_attributes_defaults.items():
            if attr_name not in calculated_attributes:
                calculated_attributes[attr_name] = default_value
        
        # 屬性範圍校驗
        calculated_attributes['max_hp'] = max(1.0, calculated_attributes['max_hp'])
        calculated_attributes['max_mp'] = max(0.0, calculated_attributes['max_mp'])
        calculated_attributes['crit_rate'] = max(0.0, min(1.0, calculated_attributes['crit_rate']))
        calculated_attributes['accuracy'] = max(0.0, min(1.0, calculated_attributes['accuracy']))
        calculated_attributes['evasion'] = max(0.0, min(1.0, calculated_attributes['evasion']))
        calculated_attributes['crit_dmg_multiplier'] = max(1.0, calculated_attributes['crit_dmg_multiplier'])
