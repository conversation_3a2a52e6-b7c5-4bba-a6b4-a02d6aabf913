"""
被動技能配置的Pydantic模型（增量式格式）
"""

from typing import List, Optional, Dict
from pydantic import BaseModel, Field

from .base_models import EffectDefinition, TriggerCondition, TargetOverride


class XpToNextLevelConfig(BaseModel):
    """技能升級XP配置模型"""
    base_xp: int = Field(..., ge=1)
    multiplier: float = Field(..., gt=1.0)


class PassiveEffectBlock(BaseModel):
    """被動技能效果塊模型"""
    trigger_condition: TriggerCondition
    target_override: Optional[TargetOverride] = None
    effect_definitions: List[EffectDefinition]


class PassiveSkillConfig(BaseModel):
    """被動技能配置模型（增量式格式）"""
    name: str
    description_template: str
    skill_rarity: int = Field(..., ge=1, le=7)
    max_level: int = Field(..., ge=1)
    base_effects: List[PassiveEffectBlock]
    xp_gain_on_sacrifice: int = Field(..., ge=0)
    xp_to_next_level_config: XpToNextLevelConfig
    tags: Optional[List[str]] = None


class PassiveSkillsConfig(BaseModel):
    """被動技能配置文件模型"""
    __root__: Dict[str, PassiveSkillConfig]
    
    def __iter__(self):
        return iter(self.__root__)
    
    def __getitem__(self, item):
        return self.__root__[item]
    
    def get(self, key, default=None):
        return self.__root__.get(key, default)
    
    def keys(self):
        return self.__root__.keys()
    
    def values(self):
        return self.__root__.values()
    
    def items(self):
        return self.__root__.items()
